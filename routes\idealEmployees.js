const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let tableCreated = false;

// إنشاء جدول العمال المثاليين إذا لم يكن موجوداً
const createIdealEmployeesTable = async (pool) => {
  if (tableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS ideal_employees (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) NOT NULL,
        department varchar(255) NOT NULL,
        from_period date NOT NULL,
        to_period date NOT NULL,
        evaluation_score decimal(5,2) NOT NULL,
        reward_amount decimal(10,2) NOT NULL,
        selection_reason text NOT NULL,
        notes text,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_department (department),
        KEY idx_period (from_period,to_period)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    tableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول العمال المثاليين:', error);
    throw error;
  }
};

// اختبار API للعامل المثالي
router.get('/test', (req, res) => {
  res.json({ message: 'API العامل المثالي يعمل بنجاح', timestamp: new Date().toISOString() });
});

// الحصول على جميع العمال المثاليين
router.get('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const [rows] = await pool.promise().execute(
      'SELECT * FROM ideal_employees ORDER BY from_period DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين:', error);
    res.status(500).json({ message: 'خطأ في جلب البيانات' });
  }
});

// إضافة عامل مثالي جديد
router.post('/', authenticateToken, async (req, res) => {
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;

  console.log('تم استقبال طلب POST /api/ideal-employees مع البيانات:', req.body);

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !from_period || !to_period || !evaluation_score || !reward_amount || !selection_reason) {
      return res.status(400).json({ message: 'جميع الحقول مطلوبة' });
    }

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees 
       WHERE employee_code = ? 
       AND ((from_period <= ? AND to_period >= ?) 
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [employee_code, from_period, from_period, to_period, to_period, from_period, to_period]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({ 
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.' 
      });
    }

    const [result] = await pool.promise().execute(
      `INSERT INTO ideal_employees 
       (employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes]
    );

    console.log('تم إدراج العامل المثالي بنجاح، ID:', result.insertId);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'ideal_employees',
      record_id: result.insertId.toString(),
      message: `تم إضافة عامل مثالي: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الفترة: من ${from_period} إلى ${to_period} - الدرجة: ${evaluation_score} - المكافأة: ${reward_amount}`
    });

    res.status(201).json({ 
      message: 'تم إضافة العامل المثالي بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في إضافة البيانات' });
  }
});

// تحديث عامل مثالي
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // الحصول على البيانات القديمة أولاً
    const [oldDataResult] = await pool.promise().execute(
      'SELECT * FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const oldData = oldDataResult[0];

    // استخدام البيانات القديمة كقيم افتراضية للحقول غير المرسلة
    const finalEmployeeCode = employee_code || oldData.employee_code;
    const finalEmployeeName = employee_name || oldData.employee_name;
    const finalDepartment = department || oldData.department;
    const finalFromPeriod = from_period || oldData.from_period;
    const finalToPeriod = to_period || oldData.to_period;
    const finalEvaluationScore = evaluation_score || oldData.evaluation_score;
    const finalRewardAmount = reward_amount || oldData.reward_amount;
    const finalSelectionReason = selection_reason || oldData.selection_reason;
    const finalNotes = notes !== undefined ? notes : oldData.notes;

    // التحقق من البيانات المطلوبة بعد دمج القيم القديمة
    if (!finalEmployeeCode || !finalEmployeeName || !finalDepartment || !finalFromPeriod || !finalToPeriod || !finalEvaluationScore || !finalRewardAmount || !finalSelectionReason) {
      return res.status(400).json({ message: 'جميع الحقول مطلوبة' });
    }

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف (باستثناء السجل الحالي)
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees
       WHERE employee_code = ? AND id != ?
       AND ((from_period <= ? AND to_period >= ?)
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [finalEmployeeCode, id, finalFromPeriod, finalFromPeriod, finalToPeriod, finalToPeriod, finalFromPeriod, finalToPeriod]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({ 
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.' 
      });
    }

    const [result] = await pool.promise().execute(
      `UPDATE ideal_employees SET
       employee_code = ?, employee_name = ?, department = ?, from_period = ?, to_period = ?,
       evaluation_score = ?, reward_amount = ?, selection_reason = ?, notes = ?
       WHERE id = ?`,
      [finalEmployeeCode, finalEmployeeName, finalDepartment, finalFromPeriod, finalToPeriod, finalEvaluationScore, finalRewardAmount, finalSelectionReason, finalNotes, id]
    );

    // إنشاء رسالة التغييرات التفصيلية
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      from_period: 'من تاريخ',
      to_period: 'إلى تاريخ',
      evaluation_score: 'درجة التقييم',
      reward_amount: 'مبلغ المكافأة',
      selection_reason: 'سبب الاختيار',
      notes: 'الملاحظات'
    };

    const newData = {
      employee_code: finalEmployeeCode,
      employee_name: finalEmployeeName,
      department: finalDepartment,
      from_period: finalFromPeriod,
      to_period: finalToPeriod,
      evaluation_score: finalEvaluationScore,
      reward_amount: finalRewardAmount,
      selection_reason: finalSelectionReason,
      notes: finalNotes
    };
    const editMessage = createEditMessage('عامل مثالي', oldData, newData, fieldLabels);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم تعديل عامل مثالي: ${oldData.employee_name} (كود: ${oldData.employee_code}) - ${editMessage}`
    });

    res.json({ message: 'تم تحديث العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في تحديث البيانات' });
  }
});

// حذف عامل مثالي
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // الحصول على بيانات العامل المثالي قبل الحذف
    const [idealEmployeeData] = await pool.promise().execute(
      'SELECT employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (idealEmployeeData.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const idealEmployee = idealEmployeeData[0];

    const [result] = await pool.promise().execute(
      'DELETE FROM ideal_employees WHERE id = ?',
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم حذف عامل مثالي: ${idealEmployee.employee_name} (كود: ${idealEmployee.employee_code}) - القسم: ${idealEmployee.department} - الفترة: من ${idealEmployee.from_period} إلى ${idealEmployee.to_period} - الدرجة: ${idealEmployee.evaluation_score} - المكافأة: ${idealEmployee.reward_amount}`
    });

    res.json({ message: 'تم حذف العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في حذف البيانات' });
  }
});

module.exports = router;
