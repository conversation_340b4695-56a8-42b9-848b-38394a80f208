const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let vacationsTableCreated = false;

// إنشاء جدول الإجازات إذا لم يكن موجودًا
const setupVacationsTable = async () => {
  if (vacationsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً
  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS vacations (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) DEFAULT NULL,
        vacation_type varchar(100) NOT NULL,
        vacation_date date NOT NULL,
        reason text,
        status varchar(50) DEFAULT 'pending',
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_vacation_date (vacation_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);
    
    // التحقق من وجود عمود employee_name وإضافته إذا لم يكن موجودًا
    const [columns] = await pool.promise().query(
      "SHOW COLUMNS FROM vacations LIKE 'employee_name'"
    );
    
    if (columns.length === 0) {
      await pool.promise().query(
        "ALTER TABLE vacations ADD COLUMN employee_name varchar(255) DEFAULT NULL AFTER employee_code"
      );
    }
    
    vacationsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول الإجازات:', error);
    throw error;
  }
};

// إنشاء جدول الإجازات
router.get('/setup-vacations-table', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await setupVacationsTable();
    res.json({ message: 'تم إنشاء جدول الإجازات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الإجازات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول الإجازات' });
  }
});

// الحصول على إجازات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(
      "SELECT *, DATE_FORMAT(vacation_date, '%Y-%m-%d') as vacation_date_formatted FROM vacations WHERE employee_code = ? ORDER BY vacation_date DESC",
      [employee_code]
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب إجازات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب إجازات الموظف' });
  }
});

// الحصول على إجازات قسم محدد
router.get('/department/:department', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { department } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT v.*, DATE_FORMAT(v.vacation_date, '%Y-%m-%d') as vacation_date_formatted, e.full_name as employee_name, e.department
      FROM vacations v
      JOIN employees e ON v.employee_code = e.code
      WHERE e.department = ?
      ORDER BY v.vacation_date DESC
    `, [department]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب إجازات القسم:', error);
    res.status(500).json({ error: 'فشل في جلب إجازات القسم' });
  }
});

// الحصول على الإجازات في فترة زمنية محددة
router.get('/date-range', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'يرجى توفير تاريخ البداية والنهاية' });
    }
    
    const [rows] = await pool.promise().query(`
      SELECT v.*, DATE_FORMAT(v.vacation_date, '%Y-%m-%d') as vacation_date_formatted, e.full_name as employee_name, e.department
      FROM vacations v
      LEFT JOIN employees e ON v.employee_code = e.code
      WHERE v.vacation_date BETWEEN ? AND ?
      ORDER BY v.vacation_date DESC
    `, [start_date, end_date]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الإجازات في الفترة المحددة:', error);
    res.status(500).json({ error: 'فشل في جلب الإجازات في الفترة المحددة' });
  }
});

// إضافة إجازة جديدة
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await setupVacationsTable();
    
    const {
      employee_code,
      vacation_type,
      vacation_date,
      official_type = null
    } = req.body;

    if (!employee_code || !vacation_type || !vacation_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب أن تكون موجودة' });
    }
    
    // الحصول على بيانات الموظف
    const [employeeRows] = await pool.promise().query(
      "SELECT full_name, department FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeRows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const employee_name = employeeRows[0].full_name;
    const department = employeeRows[0].department;

    // التحقق من عدم وجود إجازة مكررة في نفس التاريخ
    const [existingVacations] = await pool.promise().query(
      `SELECT id, vacation_type, vacation_date
       FROM vacations
       WHERE employee_code = ? AND vacation_date = ?`,
      [employee_code, vacation_date]
    );

    if (existingVacations.length > 0) {
      const existing = existingVacations[0];
      return res.status(400).json({
        error: `يوجد إجازة أخرى للموظف ${employee_name} في تاريخ ${existing.vacation_date}. لا يمكن إضافة إجازات مكررة.`,
        existing_vacation: {
          id: existing.id,
          type: existing.vacation_type,
          vacation_date: existing.vacation_date
        }
      });
    }
    
    const [result] = await pool.promise().query(
      `INSERT INTO vacations (
        employee_code, employee_name, department, vacation_type, vacation_date,
        official_type
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        employee_code, employee_name, department, vacation_type, vacation_date,
        official_type
      ]
    );

    // تسجيل النشاط
    const { logAction } = require('../activityLogger');
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'vacations',
      record_id: result.insertId.toString(),
      message: `تم إضافة إجازة للموظف: ${employee_name} (كود: ${employee_code}) - النوع: ${vacation_type} - التاريخ: ${vacation_date}`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name,
      vacation_type,
      vacation_date,
      official_type,
      message: 'تم إضافة الإجازة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة الإجازة:', error);
    res.status(500).json({ error: 'فشل في إضافة الإجازة' });
  }
});

// تحديث إجازة
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;

    // تنظيف البيانات المرسلة من العميل
    const updateData = cleanUpdateData(req.body);
    
    // التحقق من وجود الإجازة
    const [existingVacation] = await pool.promise().query(
      "SELECT * FROM vacations WHERE id = ?",
      [id]
    );

    if (existingVacation.length === 0) {
      return res.status(404).json({ error: 'الإجازة غير موجودة' });
    }

    // التحقق من التداخل مع إجازات أخرى (إذا تم تغيير التواريخ أو الموظف)
    if (updateData.employee_code && updateData.start_date && updateData.end_date) {
      const [conflictingVacations] = await pool.promise().query(
        `SELECT id, vacation_type, start_date, end_date, employee_name
         FROM vacations
         WHERE employee_code = ?
         AND id != ?
         AND (
           (start_date <= ? AND end_date >= ?) OR
           (start_date <= ? AND end_date >= ?) OR
           (start_date >= ? AND end_date <= ?)
         )`,
        [
          updateData.employee_code,
          id,
          updateData.start_date, updateData.start_date,
          updateData.end_date, updateData.end_date,
          updateData.start_date, updateData.end_date
        ]
      );

      if (conflictingVacations.length > 0) {
        const conflict = conflictingVacations[0];
        return res.status(400).json({
          error: `يوجد تداخل مع إجازة أخرى للموظف في الفترة من ${conflict.start_date} إلى ${conflict.end_date}. لا يمكن تحديث الإجازة لتتداخل مع إجازة أخرى.`,
          conflicting_vacation: {
            id: conflict.id,
            type: conflict.vacation_type,
            start_date: conflict.start_date,
            end_date: conflict.end_date
          }
        });
      }
    }
    
    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف
    if (updateData.employee_code && updateData.employee_code !== existingVacation[0].employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT full_name, department FROM employees WHERE code = ?",
        [updateData.employee_code]
      );

      if (employeeRows.length > 0) {
        updateData.employee_name = employeeRows[0].full_name;
        updateData.department = employeeRows[0].department;
      }
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'vacation_date', 'start_date', 'end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    // الحصول على بيانات الإجازة قبل التحديث
    const [vacationData] = await pool.promise().query(
      "SELECT employee_code, employee_name, vacation_type, vacation_date FROM vacations WHERE id = ?",
      [id]
    );

    await pool.promise().query(
      `UPDATE vacations SET ${setClause} WHERE id = ?`,
      values
    );

    // تسجيل النشاط
    const { logAction } = require('../activityLogger');
    if (vacationData.length > 0) {
      const vacation = vacationData[0];
      await logAction({
        user_id: req.user?.id || null,
        username: req.user?.username || 'مجهول',
        action_type: 'edit',
        module: 'vacations',
        record_id: id.toString(),
        message: `تم تحديث إجازة للموظف: ${vacation.employee_name || 'غير محدد'} (كود: ${vacation.employee_code}) - النوع: ${vacation.vacation_type} - التاريخ: ${vacation.vacation_date}`
      });
    }

    res.json({ message: 'تم تحديث الإجازة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الإجازة:', error);
    res.status(500).json({ error: 'فشل في تحديث الإجازة' });
  }
});

// حذف إجازة
router.delete('/:id', authenticateToken, checkPermission('delete_vacation'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على بيانات الإجازة قبل الحذف لتسجيل النشاط
    const [vacationData] = await pool.promise().query(
      "SELECT employee_code, employee_name, vacation_type, vacation_date FROM vacations WHERE id = ?",
      [id]
    );

    const [result] = await pool.promise().query(
      "DELETE FROM vacations WHERE id = ?",
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الإجازة غير موجودة' });
    }

    // تسجيل النشاط
    if (vacationData.length > 0) {
      const vacation = vacationData[0];
      const { logAction } = require('../activityLogger');
      await logAction({
        user_id: req.user?.id || null,
        username: req.user?.username || 'مجهول',
        action_type: 'delete',
        module: 'vacations',
        record_id: id.toString(),
        message: `تم حذف إجازة للموظف: ${vacation.employee_name || 'غير محدد'} (كود: ${vacation.employee_code}) - النوع: ${vacation.vacation_type} - التاريخ: ${vacation.vacation_date}`
      });
    }

    res.json({ message: 'تم حذف الإجازة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الإجازة:', error);
    res.status(500).json({ error: 'فشل في حذف الإجازة' });
  }
});

// الحصول على جميع الإجازات
router.get('/', authenticateToken, checkPermission('view_vacations'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(`
      SELECT v.*, e.full_name as employee_name, e.department
      FROM vacations v
      LEFT JOIN employees e ON v.employee_code = e.code
      ORDER BY v.vacation_date DESC
    `);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الإجازات:', error);
    res.status(500).json({ error: 'فشل في جلب الإجازات' });
  }
});

module.exports = router;