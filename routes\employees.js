const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");

const router = express.Router();

// الحصول على جميع الموظفين أو البحث بالاسم/الكود
router.get('/', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const { search, include_resigned } = req.query;
    let query = "SELECT * FROM employees";
    let params = [];

    // إضافة شرط الحالة (نشط فقط إلا إذا طُلب تضمين المستقيلين)
    if (include_resigned !== 'true') {
      query += " WHERE status = 'نشط'";
    }

    if (search) {
      if (include_resigned !== 'true') {
        query += " AND (full_name LIKE ? OR code LIKE ?)";
      } else {
        query += " WHERE (full_name LIKE ? OR code LIKE ?)";
      }
      params = [`%${search}%`, `%${search}%`];
    }

    query += " ORDER BY code";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    res.status(500).json({ error: 'فشل في جلب الموظفين' });
  }
});

// البحث عن الموظفين
router.post('/search', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const { searchTerm, include_resigned } = req.body;

    if (!searchTerm) {
      return res.status(400).json({ error: 'يرجى توفير مصطلح البحث' });
    }

    let query = "SELECT * FROM employees WHERE (full_name LIKE ? OR code LIKE ?)";
    let params = [`%${searchTerm}%`, `%${searchTerm}%`];

    // إضافة شرط الحالة (نشط فقط إلا إذا طُلب تضمين المستقيلين)
    if (include_resigned !== true) {
      query += " AND status = 'نشط'";
    }

    query += " ORDER BY code";

    const [rows] = await pool.promise().query(query, params);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن الموظفين:', error);
    res.status(500).json({ error: 'فشل في البحث عن الموظفين' });
  }
});

// الحصول على موظف محدد بالكود
router.get('/:code', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const { code } = req.params;

    const [rows] = await pool.promise().query(
      "SELECT * FROM employees WHERE code = ?",
      [code]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب الموظف' });
  }
});

// الحصول على الكود التالي المتاح
router.get('/next-code', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT MAX(code) as max_code FROM employees"
    );

    const nextCode = rows[0].max_code ? (parseInt(rows[0].max_code) + 1).toString() : '1';
    res.json({ nextCode });
  } catch (error) {
    console.error('خطأ في الحصول على الكود التالي:', error);
    res.status(500).json({ error: 'فشل في الحصول على الكود التالي' });
  }
});

// إضافة موظف جديد
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const {
      employee_code,
      name,
      department,
      position,
      hire_date,
      salary,
      phone,
      email,
      address,
      national_id,
      birth_date,
      gender,
      marital_status,
      emergency_contact,
      emergency_phone,
      bank_account,
      insurance_number,
      contract_type,
      work_schedule,
      supervisor,
      notes
    } = req.body;
    
    if (!employee_code || !name) {
      return res.status(400).json({ error: 'كود الموظف والاسم مطلوبان' });
    }
    
    // التحقق من عدم وجود موظف بنفس الكود
    const [existingEmployee] = await pool.promise().query(
      "SELECT code FROM employees WHERE code = ?",
      [employee_code]
    );

    if (existingEmployee.length > 0) {
      return res.status(400).json({ error: 'كود الموظف موجود بالفعل' });
    }
    
    const [result] = await pool.promise().query(
      `INSERT INTO employees (
        code, full_name, department, job_title, hire_date, phone, address,
        national_id, birth_date, marital_status, insurance_number
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, name, department, position, hire_date, phone, address,
        national_id, birth_date, marital_status, insurance_number
      ]
    );

    // تسجيل النشاط
    const { logAction } = require('../activityLogger');
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'employees',
      record_id: employee_code,
      message: `تم إضافة موظف جديد: ${name} (كود: ${employee_code})`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      name,
      message: 'تم إضافة الموظف بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    res.status(500).json({ error: 'فشل في إضافة الموظف' });
  }
});

// تحديث موظف
router.put('/:code', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { code } = req.params;
    const updateData = req.body;

    // التحقق من وجود الموظف
    const [existingEmployee] = await pool.promise().query(
      "SELECT code FROM employees WHERE code = ?",
      [code]
    );

    if (existingEmployee.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;

    // إذا تم تغيير كود الموظف، التحقق من عدم وجود موظف آخر بنفس الكود
    if (updateData.employee_code && updateData.employee_code !== code) {
      const [duplicateEmployee] = await pool.promise().query(
        "SELECT code FROM employees WHERE code = ?",
        [updateData.employee_code]
      );

      if (duplicateEmployee.length > 0) {
        return res.status(400).json({ error: 'كود الموظف الجديد موجود بالفعل' });
      }
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'birth_date', 'hire_date', 'insurance_start', 'skill_start', 'skill_end',
      'vacation_date', 'start_date', 'end_date', 'resignation_date',
      'work_end_date', 'delivery_date', 'penalty_date', 'evaluation_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(code);

    // الحصول على اسم الموظف قبل التحديث
    const [employeeData] = await pool.promise().query(
      "SELECT full_name FROM employees WHERE code = ?",
      [code]
    );

    await pool.promise().query(
      `UPDATE employees SET ${setClause} WHERE code = ?`,
      values
    );

    // تسجيل النشاط
    const { logAction } = require('../activityLogger');
    const employeeName = employeeData.length > 0 ? employeeData[0].full_name : 'غير محدد';
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'employees',
      record_id: code,
      message: `تم تحديث بيانات الموظف: ${employeeName} (كود: ${code})`
    });

    res.json({ message: 'تم تحديث الموظف بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الموظف:', error);
    res.status(500).json({ error: 'فشل في تحديث الموظف' });
  }
});

// حذف موظف محدد
router.delete('/:code', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { code } = req.params;

    // الحصول على بيانات الموظف قبل الحذف
    const [employeeData] = await pool.promise().query(
      "SELECT full_name FROM employees WHERE code = ?",
      [code]
    );

    const [result] = await pool.promise().query(
      "DELETE FROM employees WHERE code = ?",
      [code]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    // تسجيل النشاط
    const { logAction } = require('../activityLogger');
    const employeeName = employeeData.length > 0 ? employeeData[0].full_name : 'غير محدد';
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'employees',
      record_id: code,
      message: `تم حذف الموظف: ${employeeName} (كود: ${code})`
    });

    res.json({ message: 'تم حذف الموظف بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الموظف:', error);
    res.status(500).json({ error: 'فشل في حذف الموظف' });
  }
});

// حذف جميع الموظفين
router.delete('/', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    await pool.promise().query("DELETE FROM employees");
    res.json({ message: 'تم حذف جميع الموظفين بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف جميع الموظفين:', error);
    res.status(500).json({ error: 'فشل في حذف جميع الموظفين' });
  }
});

// حساب رصيد الإجازات
router.post('/calculate-leave-balance', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const [employees] = await pool.promise().query("SELECT * FROM employees");
    
    for (const employee of employees) {
      let annualLeave = 21; // الإجازة السنوية الافتراضية
      
      if (employee.hire_date) {
        const hireDate = new Date(employee.hire_date);
        const currentDate = new Date();
        
        // التحقق من صحة تاريخ التوظيف
        if (!isNaN(hireDate.getTime()) && hireDate <= currentDate) {
          const yearsOfService = Math.floor((currentDate - hireDate) / (365.25 * 24 * 60 * 60 * 1000));
          
          // حساب الإجازة السنوية بناءً على سنوات الخدمة
          if (yearsOfService >= 10) {
            annualLeave = 30;
          } else if (yearsOfService >= 5) {
            annualLeave = 25;
          } else {
            annualLeave = 21;
          }
        } else {
          console.log(`تاريخ توظيف غير صحيح للموظف ${employee.name}: ${employee.hire_date}`);
        }
      } else {
        console.log(`لا يوجد تاريخ توظيف للموظف ${employee.name}`);
      }
      
      // تحديث رصيد الإجازات
      await pool.promise().query(
        "UPDATE employees SET annual_leave = ? WHERE id = ?",
        [annualLeave, employee.id]
      );
    }
    
    res.json({ message: 'تم حساب وتحديث رصيد الإجازات لجميع الموظفين بنجاح' });
  } catch (error) {
    console.error('خطأ في حساب رصيد الإجازات:', error);
    res.status(500).json({ error: 'فشل في حساب رصيد الإجازات' });
  }
});

// الحصول على الأقسام
router.get('/departments', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT DISTINCT department FROM employees WHERE department IS NOT NULL AND department != '' ORDER BY department"
    );
    
    const departments = rows.map(row => row.department);
    res.json(departments);
  } catch (error) {
    console.error('خطأ في جلب الأقسام:', error);
    res.status(500).json({ error: 'فشل في جلب الأقسام' });
  }
});

module.exports = router;